import os
import sys
import datetime
import threading
import unicodedata
import json
from typing import Any, Dict, List, Optional, Tuple
from flask import Flask, render_template, request, jsonify, send_file, session
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import uuid

try:
    import pyodbc
except ImportError:
    print("Erro: A biblioteca 'pyodbc' não está instalada. Execute: pip install pyodbc")
    sys.exit(1)

# Importar funções do arquivo original
from mdb_to_postgres import (
    find_access_driver, connect_mdb, normalize_name, quote_ident,
    is_quantidade_field, is_hora_field, ACCESS_TO_PG_TYPE,
    odbc_type_to_pg, get_tables, get_columns, get_primary_keys,
    format_value, generate_create_table, generate_inserts
)

app = Flask(__name__)
app.secret_key = 'sua_chave_secreta_aqui'  # Mude para uma chave segura em produção
CORS(app)

# Configurações
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'mdb', 'accdb'}
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB

# Criar pasta de uploads se não existir
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Tipo de arquivo não permitido. Use .mdb ou .accdb'}), 400
        
        # Gerar nome único para o arquivo
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
        
        file.save(filepath)
        
        # Armazenar informações na sessão
        session['uploaded_file'] = filepath
        session['original_filename'] = filename
        
        return jsonify({
            'success': True,
            'message': 'Arquivo enviado com sucesso',
            'filename': filename,
            'file_id': unique_filename
        })
        
    except Exception as e:
        return jsonify({'error': f'Erro ao fazer upload: {str(e)}'}), 500

@app.route('/convert', methods=['POST'])
def convert_database():
    try:
        if 'uploaded_file' not in session:
            return jsonify({'error': 'Nenhum arquivo foi enviado'}), 400
        
        db_path = session['uploaded_file']
        if not os.path.exists(db_path):
            return jsonify({'error': 'Arquivo não encontrado'}), 400
        
        # Conectar ao banco
        conn = connect_mdb(db_path)
        cur = conn.cursor()
        
        # Buscar tabelas
        tables = get_tables(cur)
        if not tables:
            return jsonify({'error': 'Nenhuma tabela encontrada no banco de dados'}), 400
        
        # Gerar SQL
        statements = []
        statements.append("-- SQL gerado a partir do Access para PostgreSQL")
        statements.append(f"-- Fonte: {session.get('original_filename', 'arquivo')}")
        statements.append(f"-- Gerado em: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        statements.append("SET standard_conforming_strings = on;")
        
        # Criar tabelas
        for table in tables:
            cols = get_columns(cur, table)
            if not cols:
                continue
            pks = get_primary_keys(cur, table)
            statements.append(generate_create_table(table, cols, pks))
        
        statements.append("\n-- ================= DATA =================")
        
        # Inserir dados
        for table in tables:
            cols = get_columns(cur, table)
            if not cols:
                continue
            inserts = generate_inserts(cur, table, cols)
            if inserts:
                statements.append(f"\n-- Tabela: {quote_ident(table)}")
                statements.extend(inserts)
        
        sql_content = "\n".join(statements) + "\n"
        
        # Salvar arquivo SQL temporário
        temp_dir = tempfile.gettempdir()
        output_filename = f"{os.path.splitext(session.get('original_filename', 'database'))[0]}_postgres.sql"
        output_path = os.path.join(temp_dir, f"{uuid.uuid4()}_{output_filename}")
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(sql_content)
        
        # Armazenar caminho do arquivo de saída na sessão
        session['output_file'] = output_path
        session['output_filename'] = output_filename
        
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Conversão realizada com sucesso',
            'tables_count': len(tables),
            'tables': tables,
            'download_ready': True
        })
        
    except Exception as e:
        return jsonify({'error': f'Erro na conversão: {str(e)}'}), 500

@app.route('/download')
def download_file():
    try:
        if 'output_file' not in session:
            return jsonify({'error': 'Nenhum arquivo para download'}), 400
        
        output_path = session['output_file']
        output_filename = session.get('output_filename', 'database_postgres.sql')
        
        if not os.path.exists(output_path):
            return jsonify({'error': 'Arquivo não encontrado'}), 404
        
        return send_file(
            output_path,
            as_attachment=True,
            download_name=output_filename,
            mimetype='text/plain'
        )
        
    except Exception as e:
        return jsonify({'error': f'Erro no download: {str(e)}'}), 500

@app.route('/status')
def get_status():
    """Endpoint para verificar status da aplicação"""
    return jsonify({
        'status': 'online',
        'driver_available': find_access_driver() is not None,
        'driver_name': find_access_driver()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
