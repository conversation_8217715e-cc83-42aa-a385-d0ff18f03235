/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 32px;
    color: #667eea;
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
    color: #2d3748;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    background: #f7fafc;
    font-size: 14px;
    font-weight: 500;
}

.status-indicator.online {
    background: #c6f6d5;
    color: #22543d;
}

.status-indicator.online i {
    color: #38a169;
}

.status-indicator.offline {
    background: #fed7d7;
    color: #742a2a;
}

.status-indicator.offline i {
    color: #e53e3e;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Step Cards */
.step-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    opacity: 0.6;
    transform: scale(0.98);
}

.step-card.active {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.step-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.step-info h2 {
    font-size: 22px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.step-info p {
    color: #718096;
    font-size: 14px;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.upload-area:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.upload-area.dragover {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: scale(1.02);
}

.upload-content i {
    font-size: 48px;
    color: #a0aec0;
    margin-bottom: 16px;
}

.upload-content h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.upload-content p {
    color: #718096;
    margin-bottom: 20px;
}

.file-types {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.file-type {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

/* File Info */
.file-info {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 12px;
    padding: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 16px;
}

.file-details i {
    font-size: 32px;
    color: #38a169;
}

.file-text {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.file-size {
    font-size: 14px;
    color: #718096;
}

.btn-remove {
    background: #fed7d7;
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    cursor: pointer;
    color: #e53e3e;
    transition: all 0.2s ease;
}

.btn-remove:hover {
    background: #feb2b2;
}

/* Buttons */
.btn-primary, .btn-success {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
    min-width: 200px;
    margin: 0 auto;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
}

.btn-primary:hover, .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary:disabled, .btn-success:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Progress */
.progress-container {
    margin-top: 20px;
}

.progress-bar {
    background: #e2e8f0;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    background: linear-gradient(90deg, #667eea, #764ba2);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    color: #718096;
    font-size: 14px;
    font-weight: 500;
}

/* Conversion Summary */
.conversion-summary {
    background: #f0fff4;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    color: #2d3748;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-item i {
    color: #38a169;
    font-size: 18px;
}

/* Notifications */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    background: white;
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid #38a169;
}

.notification.error {
    border-left: 4px solid #e53e3e;
}

.notification.info {
    border-left: 4px solid #3182ce;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .step-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .step-card {
        padding: 20px;
    }

    .upload-area {
        padding: 30px 15px;
    }
}
