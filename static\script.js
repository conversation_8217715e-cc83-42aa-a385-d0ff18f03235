class MDBConverter {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.checkStatus();
    }

    initializeElements() {
        // Upload elements
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.fileInfo = document.getElementById('fileInfo');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        this.removeFileBtn = document.getElementById('removeFile');

        // Step elements
        this.uploadStep = document.getElementById('uploadStep');
        this.convertStep = document.getElementById('convertStep');
        this.downloadStep = document.getElementById('downloadStep');

        // Convert elements
        this.convertBtn = document.getElementById('convertBtn');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');

        // Download elements
        this.downloadBtn = document.getElementById('downloadBtn');
        this.conversionSummary = document.getElementById('conversionSummary');
        this.tablesCount = document.getElementById('tablesCount');

        // Status
        this.statusIndicator = document.getElementById('statusIndicator');
        this.notifications = document.getElementById('notifications');
    }

    attachEventListeners() {
        // Upload events
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.removeFileBtn.addEventListener('click', () => this.removeFile());

        // Drag and drop
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));

        // Convert button
        this.convertBtn.addEventListener('click', () => this.startConversion());

        // Download button
        this.downloadBtn.addEventListener('click', () => this.downloadFile());
    }

    async checkStatus() {
        try {
            const response = await fetch('/status');
            const data = await response.json();
            
            if (data.driver_available) {
                this.updateStatus('online', `Driver: ${data.driver_name}`);
            } else {
                this.updateStatus('offline', 'Driver ODBC não encontrado');
                this.showNotification('error', 'Driver ODBC do Access não encontrado. Instale o Microsoft Access Database Engine.');
            }
        } catch (error) {
            this.updateStatus('offline', 'Erro de conexão');
            this.showNotification('error', 'Erro ao verificar status do servidor');
        }
    }

    updateStatus(status, message) {
        this.statusIndicator.className = `status-indicator ${status}`;
        this.statusIndicator.querySelector('span').textContent = message;
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    processFile(file) {
        // Validar tipo de arquivo
        const allowedTypes = ['mdb', 'accdb'];
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showNotification('error', 'Tipo de arquivo não suportado. Use arquivos .mdb ou .accdb');
            return;
        }

        // Validar tamanho (100MB max)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showNotification('error', 'Arquivo muito grande. Tamanho máximo: 100MB');
            return;
        }

        this.uploadFile(file);
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            this.showProgress('Enviando arquivo...', 20);
            
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.showFileInfo(file);
                this.activateStep('convert');
                this.convertBtn.disabled = false;
                this.showNotification('success', 'Arquivo enviado com sucesso!');
                this.hideProgress();
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            this.showNotification('error', `Erro no upload: ${error.message}`);
            this.hideProgress();
        }
    }

    showFileInfo(file) {
        this.fileName.textContent = file.name;
        this.fileSize.textContent = this.formatFileSize(file.size);
        this.uploadArea.style.display = 'none';
        this.fileInfo.style.display = 'block';
    }

    removeFile() {
        this.uploadArea.style.display = 'block';
        this.fileInfo.style.display = 'none';
        this.fileInput.value = '';
        this.convertBtn.disabled = true;
        this.activateStep('upload');
        this.hideProgress();
    }

    async startConversion() {
        try {
            this.convertBtn.disabled = true;
            this.convertBtn.innerHTML = '<div class="loading"></div> Convertendo...';
            this.showProgress('Iniciando conversão...', 30);

            const response = await fetch('/convert', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showProgress('Conversão concluída!', 100);
                this.showConversionSummary(data);
                this.activateStep('download');
                this.downloadBtn.disabled = false;
                this.showNotification('success', `Conversão concluída! ${data.tables_count} tabelas processadas.`);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            this.showNotification('error', `Erro na conversão: ${error.message}`);
            this.convertBtn.disabled = false;
            this.convertBtn.innerHTML = '<i class="fas fa-magic"></i> Iniciar Conversão';
        }
    }

    showConversionSummary(data) {
        this.tablesCount.textContent = data.tables_count;
        this.conversionSummary.style.display = 'block';
    }

    async downloadFile() {
        try {
            this.downloadBtn.innerHTML = '<div class="loading"></div> Baixando...';
            
            const response = await fetch('/download');
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1] || 'database_postgres.sql';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showNotification('success', 'Download concluído!');
            } else {
                const data = await response.json();
                throw new Error(data.error);
            }
        } catch (error) {
            this.showNotification('error', `Erro no download: ${error.message}`);
        } finally {
            this.downloadBtn.innerHTML = '<i class="fas fa-download"></i> Baixar Arquivo SQL';
        }
    }

    activateStep(step) {
        // Remove active class from all steps
        document.querySelectorAll('.step-card').forEach(card => {
            card.classList.remove('active');
        });

        // Add active class to current step
        switch (step) {
            case 'upload':
                this.uploadStep.classList.add('active');
                break;
            case 'convert':
                this.convertStep.classList.add('active');
                break;
            case 'download':
                this.downloadStep.classList.add('active');
                break;
        }
    }

    showProgress(text, percentage) {
        this.progressContainer.style.display = 'block';
        this.progressText.textContent = text;
        this.progressFill.style.width = `${percentage}%`;
    }

    hideProgress() {
        this.progressContainer.style.display = 'none';
        this.progressFill.style.width = '0%';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const icon = type === 'success' ? 'fas fa-check-circle' : 
                    type === 'error' ? 'fas fa-exclamation-circle' : 
                    'fas fa-info-circle';
        
        notification.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;

        this.notifications.appendChild(notification);

        // Remove notification after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MDBConverter();
});
