<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversor MDB para PostgreSQL</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-database"></i>
                    <h1>MDB → PostgreSQL</h1>
                </div>
                <div class="status-indicator" id="statusIndicator">
                    <i class="fas fa-circle"></i>
                    <span>Verificando...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: Upload -->
            <div class="step-card active" id="uploadStep">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <div class="step-info">
                        <h2>Selecionar Arquivo</h2>
                        <p>Faça upload do seu banco de dados Access (.mdb ou .accdb)</p>
                    </div>
                </div>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>Arraste seu arquivo aqui</h3>
                        <p>ou clique para selecionar</p>
                        <div class="file-types">
                            <span class="file-type">.MDB</span>
                            <span class="file-type">.ACCDB</span>
                        </div>
                    </div>
                    <input type="file" id="fileInput" accept=".mdb,.accdb" hidden>
                </div>

                <div class="file-info" id="fileInfo" style="display: none;">
                    <div class="file-details">
                        <i class="fas fa-file-database"></i>
                        <div class="file-text">
                            <div class="file-name" id="fileName"></div>
                            <div class="file-size" id="fileSize"></div>
                        </div>
                        <button class="btn-remove" id="removeFile">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 2: Convert -->
            <div class="step-card" id="convertStep">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <div class="step-info">
                        <h2>Converter</h2>
                        <p>Transformar o banco Access em script PostgreSQL</p>
                    </div>
                </div>
                
                <div class="convert-content">
                    <button class="btn-primary" id="convertBtn" disabled>
                        <i class="fas fa-magic"></i>
                        Iniciar Conversão
                    </button>
                    
                    <div class="progress-container" id="progressContainer" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">Preparando conversão...</div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Download -->
            <div class="step-card" id="downloadStep">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <div class="step-info">
                        <h2>Download</h2>
                        <p>Baixar o arquivo SQL gerado</p>
                    </div>
                </div>
                
                <div class="download-content" id="downloadContent">
                    <div class="conversion-summary" id="conversionSummary" style="display: none;">
                        <div class="summary-item">
                            <i class="fas fa-table"></i>
                            <span>Tabelas convertidas: <strong id="tablesCount">0</strong></span>
                        </div>
                        <div class="summary-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Conversão concluída com sucesso!</span>
                        </div>
                    </div>
                    
                    <button class="btn-success" id="downloadBtn" disabled>
                        <i class="fas fa-download"></i>
                        Baixar Arquivo SQL
                    </button>
                </div>
            </div>
        </main>

        <!-- Notifications -->
        <div class="notifications" id="notifications"></div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
